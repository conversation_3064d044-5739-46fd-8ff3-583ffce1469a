using UnityEngine;

public class TerrainPainter : MonoBehaviour
{
    public float paintRadius = 2f;              // Radius of texture painting
    public int textureIndexToPaint = 1;         // Index of the texture to apply (e.g., ploughed soil)
    public int selectLayer = 0;                 // Which layer to select/change texture on
    public int applyLayer = 1;                  // Which texture layer to apply on selected layer
    
    private Terrain terrain;
    private TerrainData terrainData;
    private float[,,] originalAlphamaps;        // Backup of original textures

    void Start()
    {
        terrain = Terrain.activeTerrain;

        if (terrain != null)
        {
            terrainData = terrain.terrainData;

            // Backup original textures for resetting later
            originalAlphamaps = terrainData.GetAlphamaps(0, 0, 
                terrainData.alphamapWidth, terrainData.alphamapHeight);
        }
    }

    void Update()
    {
        if (terrain == null) return;

        // Raycast from cube's position straight down
        Ray ray = new Ray(transform.position, Vector3.down);
        if (Physics.Raycast(ray, out RaycastHit hit))
        {
            if (hit.collider.GetComponent<Terrain>())
            {
                PaintTextureAt(hit.point);
            }
        }

        // Reset to original texture with R key
        if (Input.GetKeyDown(KeyCode.R))
        {
            ResetTerrainTexture();
        }
    }

    void PaintTextureAt(Vector3 worldPos)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        int radius = Mathf.RoundToInt(paintRadius);
        int size = radius * 2;

        // Clamp area within terrain bounds
        int startX = Mathf.Clamp(mapX - radius, 0, terrainData.alphamapWidth - size);
        int startZ = Mathf.Clamp(mapZ - radius, 0, terrainData.alphamapHeight - size);

        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, size, size);

        for (int x = 0; x < size; x++)
        {
            for (int z = 0; z < size; z++)
            {
                // Only modify the selected layer, keep other layers unchanged
                if (selectLayer < terrainData.alphamapLayers)
                {
                    // Set the apply layer texture on the selected layer
                    if (applyLayer < terrainData.alphamapLayers)
                    {
                        alphaMaps[x, z, selectLayer] = (selectLayer == applyLayer) ? 1f : 0f;

                        // If we're applying a different layer, reduce the selected layer and increase the apply layer
                        if (selectLayer != applyLayer)
                        {
                            alphaMaps[x, z, applyLayer] = 1f;
                            alphaMaps[x, z, selectLayer] = 0f;
                        }
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);
    }

    void ResetTerrainTexture()
    {
        terrainData.SetAlphamaps(0, 0, originalAlphamaps);
        Debug.Log("Terrain texture reset to original.");
    }
}
